import { NavLink, <PERSON> } from "react-router-dom";
import logo from "../assets/Logo.png";
const Nav = () => {
  return (
    <nav className="w-[70%] h-15 bg-black/40 backdrop-blur-md border border-white/20 rounded-xl flex justify-between items-center absolute top-2 left-1/2 -translate-x-1/2 shadow-lg px-3 py-2">
      <img src={logo} alt="logo" className="w-15 h-fit" />
      <div className="flex gap-5 items-center">
        <NavLink className={({ isActive }) => (isActive ? "text-white" : "text-white/70")} to="/">Home</NavLink>
        <NavLink className={({ isActive }) => (isActive ? "text-white" : "text-white/70")} to="/Category">Category</NavLink>
        <NavLink className={({ isActive }) => (isActive ? "text-white" : "text-white/70")} to="/Collection">Collection</NavLink>
        <NavLink className={({ isActive }) => (isActive ? "text-white" : "text-white/70")} to="/FitGuide">Fit Guide</NavLink>
      </div>
      <div className="icons h-fit flex justify-between gap-5 items-center text-2xl text-white">
        <i className="ri-search-2-line cursor-pointer hover:scale-125"></i>
        <i className="ri-user-3-line cursor-pointer hover:scale-125"></i>
        <i className="ri-shopping-cart-2-line cursor-pointer hover:scale-125"></i>
      </div>
    </nav>
  );
};

export default Nav;
