import { useLayoutEffect, useRef, useCallback, useMemo } from "react";
import <PERSON><PERSON> from "lenis";

// Utility functions for performance optimization
const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
};

const lerp = (start, end, factor) => start + (end - start) * factor;

const easeOutCubic = (t) => 1 - Math.pow(1 - t, 3);
const easeInOutCubic = (t) => t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;

export const ScrollStackItem = ({ children, itemClassName = "" }) => (
  <div
    className={`scroll-stack-card relative w-full h-fit my-8 overflow-hidden rounded-2xl shadow-[0_0_30px_rgba(0,0,0,0.1)] box-border origin-top will-change-transform transition-all duration-300 hover:shadow-[0_0_40px_rgba(0,0,0,0.2)] cursor-pointer group ${itemClassName}`.trim()}
    style={{
      backfaceVisibility: 'hidden',
      transformStyle: 'preserve-3d',
      contain: 'layout style paint',
    }}
  >
    <div className="relative z-10 transition-transform duration-300 group-hover:scale-[1.02]">
      {children}
    </div>
    {/* Glassmorphism overlay for better visual appeal */}
    <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
  </div>
);

const ScrollStack = ({
  children,
  className = "",
  itemDistance = 100,
  itemScale = 0.03,
  itemStackDistance = 30,
  stackPosition = "20%",
  scaleEndPosition = "10%",
  baseScale = 0.85,
  scaleDuration = 0.5,
  rotationAmount = 0,
  blurAmount = 0,
  onStackComplete,
}) => {
  const scrollerRef = useRef(null);
  const stackCompletedRef = useRef(false);
  const animationFrameRef = useRef(null);
  const lenisRef = useRef(null);
  const cardsRef = useRef([]);
  const lastTransformsRef = useRef(new Map());
  const isUpdatingRef = useRef(false);
  const resizeObserverRef = useRef(null);
  const cachedDimensionsRef = useRef({});
  const smoothTransformsRef = useRef(new Map());

  const calculateProgress = useCallback((scrollTop, start, end, easingFunction = easeInOutCubic) => {
    if (scrollTop < start) return 0;
    if (scrollTop > end) return 1;
    const linearProgress = (scrollTop - start) / (end - start);
    return easingFunction(linearProgress);
  }, []);

  const parsePercentage = useCallback((value, containerHeight) => {
    if (typeof value === 'string' && value.includes('%')) {
      return (parseFloat(value) / 100) * containerHeight;
    }
    return parseFloat(value);
  }, []);

  // Responsive calculations based on screen size
  const getResponsiveValues = useCallback(() => {
    const width = window.innerWidth;
    const isMobile = width < 768;
    const isTablet = width >= 768 && width < 1024;

    return {
      itemDistance: isMobile ? itemDistance * 0.6 : isTablet ? itemDistance * 0.8 : itemDistance,
      itemScale: isMobile ? itemScale * 0.5 : isTablet ? itemScale * 0.7 : itemScale,
      itemStackDistance: isMobile ? itemStackDistance * 0.7 : itemStackDistance,
      blurAmount: isMobile ? blurAmount * 0.5 : blurAmount,
      rotationAmount: isMobile ? rotationAmount * 0.5 : rotationAmount,
    };
  }, [itemDistance, itemScale, itemStackDistance, blurAmount, rotationAmount]);

  const updateCardTransforms = useCallback(() => {
    const scroller = scrollerRef.current;
    if (!scroller || !cardsRef.current.length || isUpdatingRef.current) return;

    isUpdatingRef.current = true;

    const scrollTop = scroller.scrollTop;
    const containerHeight = scroller.clientHeight;
    const stackPositionPx = parsePercentage(stackPosition, containerHeight);
    const scaleEndPositionPx = parsePercentage(scaleEndPosition, containerHeight);
    const endElement = scroller.querySelector('.scroll-stack-end');
    const endElementTop = endElement ? endElement.offsetTop : 0;

    // Get responsive values for current screen size
    const responsiveValues = getResponsiveValues();

    // Use requestAnimationFrame for smoother animations
    const performanceNow = performance.now();

    cardsRef.current.forEach((card, i) => {
      if (!card) return;

      const cardTop = card.offsetTop;
      const triggerStart = cardTop - stackPositionPx - (responsiveValues.itemStackDistance * i);
      const triggerEnd = cardTop - scaleEndPositionPx;
      const pinStart = cardTop - stackPositionPx - (responsiveValues.itemStackDistance * i);
      const pinEnd = endElementTop - containerHeight / 2;

      const scaleProgress = calculateProgress(scrollTop, triggerStart, triggerEnd, easeOutCubic);
      const targetScale = baseScale + (i * responsiveValues.itemScale);
      const scale = 1 - scaleProgress * (1 - targetScale);
      const rotation = responsiveValues.rotationAmount ? i * responsiveValues.rotationAmount * scaleProgress : 0;

      let blur = 0;
      if (responsiveValues.blurAmount) {
        let topCardIndex = 0;
        for (let j = 0; j < cardsRef.current.length; j++) {
          const jCardTop = cardsRef.current[j].offsetTop;
          const jTriggerStart = jCardTop - stackPositionPx - (responsiveValues.itemStackDistance * j);
          if (scrollTop >= jTriggerStart) {
            topCardIndex = j;
          }
        }

        if (i < topCardIndex) {
          const depthInStack = topCardIndex - i;
          blur = Math.max(0, depthInStack * responsiveValues.blurAmount);
        }
      }

      let translateY = 0;
      const isPinned = scrollTop >= pinStart && scrollTop <= pinEnd;

      if (isPinned) {
        translateY = scrollTop - cardTop + stackPositionPx + (responsiveValues.itemStackDistance * i);
      } else if (scrollTop > pinEnd) {
        // Move all cards up together after pinEnd with smooth easing
        const overscroll = scrollTop - pinEnd;
        const easedOverscroll = overscroll * easeOutCubic(Math.min(overscroll / 200, 1));
        translateY = pinEnd - cardTop + stackPositionPx + (responsiveValues.itemStackDistance * i) - easedOverscroll;
      }

      const targetTransform = {
        translateY: Math.round(translateY * 100) / 100,
        scale: Math.round(scale * 1000) / 1000,
        rotation: Math.round(rotation * 100) / 100,
        blur: Math.round(blur * 100) / 100
      };

      // Smooth interpolation for better animation quality
      const lastSmooth = smoothTransformsRef.current.get(i) || targetTransform;
      const lerpFactor = 0.15; // Adjust for smoothness vs responsiveness

      const newTransform = {
        translateY: lerp(lastSmooth.translateY, targetTransform.translateY, lerpFactor),
        scale: lerp(lastSmooth.scale, targetTransform.scale, lerpFactor),
        rotation: lerp(lastSmooth.rotation, targetTransform.rotation, lerpFactor),
        blur: lerp(lastSmooth.blur, targetTransform.blur, lerpFactor)
      };

      smoothTransformsRef.current.set(i, newTransform);

      const lastTransform = lastTransformsRef.current.get(i);
      const hasChanged = !lastTransform ||
        Math.abs(lastTransform.translateY - newTransform.translateY) > 0.1 ||
        Math.abs(lastTransform.scale - newTransform.scale) > 0.001 ||
        Math.abs(lastTransform.rotation - newTransform.rotation) > 0.1 ||
        Math.abs(lastTransform.blur - newTransform.blur) > 0.1;

      if (hasChanged) {
        // Use GPU-accelerated transforms for better performance
        const transform = `translate3d(0, ${newTransform.translateY}px, 0) scale3d(${newTransform.scale}, ${newTransform.scale}, 1) rotateZ(${newTransform.rotation}deg)`;
        const filter = newTransform.blur > 0 ? `blur(${newTransform.blur}px)` : '';

        // Batch DOM updates for better performance
        card.style.cssText = `
          transform: ${transform};
          filter: ${filter};
          will-change: transform, filter;
          backface-visibility: hidden;
          transform-style: preserve-3d;
        `;

        lastTransformsRef.current.set(i, newTransform);
      }

      if (i === cardsRef.current.length - 1) {
        const isInView = scrollTop >= pinStart && scrollTop <= pinEnd;
        if (isInView && !stackCompletedRef.current) {
          stackCompletedRef.current = true;
          onStackComplete?.();
        } else if (!isInView && stackCompletedRef.current) {
          stackCompletedRef.current = false;
        }
      }
    });

    isUpdatingRef.current = false;
  }, [
    itemScale,
    itemStackDistance,
    stackPosition,
    scaleEndPosition,
    baseScale,
    rotationAmount,
    blurAmount,
    onStackComplete,
    calculateProgress,
    parsePercentage,
  ]);

  // Throttled scroll handler for better performance
  const handleScroll = useCallback(
    throttle(() => {
      updateCardTransforms();
    }, 16), // ~60fps
    [updateCardTransforms]
  );

  const setupLenis = useCallback(() => {
    const scroller = scrollerRef.current;
    if (!scroller) return;

    const isMobile = window.innerWidth < 768;

    const lenis = new Lenis({
      wrapper: scroller,
      content: scroller.querySelector('.scroll-stack-inner'),
      duration: isMobile ? 0.8 : 1.2,
      easing: (t) => easeInOutCubic(t),
      smoothWheel: true,
      touchMultiplier: isMobile ? 1.5 : 2,
      infinite: false,
      wheelMultiplier: isMobile ? 0.8 : 1,
      lerp: isMobile ? 0.15 : 0.1,
      syncTouch: true,
      syncTouchLerp: isMobile ? 0.1 : 0.075,
      gestureOrientation: 'vertical',
      normalizeWheel: true,
    });

    lenis.on('scroll', handleScroll);

    const raf = (time) => {
      lenis.raf(time);
      animationFrameRef.current = requestAnimationFrame(raf);
    };
    animationFrameRef.current = requestAnimationFrame(raf);

    lenisRef.current = lenis;
    return lenis;
  }, [handleScroll]);

  useLayoutEffect(() => {
    const scroller = scrollerRef.current;
    if (!scroller) return;

    const cards = Array.from(scroller.querySelectorAll(".scroll-stack-card"));
    cardsRef.current = cards;
    const transformsCache = lastTransformsRef.current;

    const responsiveValues = getResponsiveValues();

    cards.forEach((card, i) => {
      if (i < cards.length - 1) {
        card.style.marginBottom = `${responsiveValues.itemDistance}px`;
      }
      card.style.willChange = 'transform, filter';
      card.style.transformOrigin = 'top center';
      card.style.backfaceVisibility = 'hidden';
      card.style.transform = 'translateZ(0)';
      card.style.webkitTransform = 'translateZ(0)';
      card.style.perspective = '1000px';
      card.style.webkitPerspective = '1000px';
      card.style.contain = 'layout style paint';
    });

    // Setup resize observer for responsive updates
    resizeObserverRef.current = new ResizeObserver(
      throttle(() => {
        cachedDimensionsRef.current = {};
        updateCardTransforms();
      }, 100)
    );

    resizeObserverRef.current.observe(scroller);

    setupLenis();

    updateCardTransforms();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (lenisRef.current) {
        lenisRef.current.destroy();
      }
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
      stackCompletedRef.current = false;
      cardsRef.current = [];
      transformsCache.clear();
      smoothTransformsRef.current.clear();
      cachedDimensionsRef.current = {};
      isUpdatingRef.current = false;
    };
  }, [
    itemDistance,
    itemScale,
    itemStackDistance,
    stackPosition,
    scaleEndPosition,
    baseScale,
    scaleDuration,
    rotationAmount,
    blurAmount,
    onStackComplete,
    setupLenis,
    updateCardTransforms,
  ]);

  return (
    <div
      className={`relative w-full h-full overflow-y-auto overflow-x-visible ${className}`.trim()}
      ref={scrollerRef}
      style={{ 
        overscrollBehavior: 'contain',
        WebkitOverflowScrolling: 'touch',
        scrollBehavior: 'smooth',
        WebkitTransform: 'translateZ(0)',
        transform: 'translateZ(0)',
        willChange: 'scroll-position',
        scrollbarWidth: 'none',
        msOverflowStyle: 'none',
      }}
    >
      <div className="scroll-stack-inner pt-[20vh] px-4 sm:px-8 md:px-12 lg:px-20 pb-[50rem] min-h-full">
        {children}
        {/* Spacer so the last pin can release cleanly */}
        <div className="scroll-stack-end w-full h-px" />
      </div>
    </div>
  );
};

export default ScrollStack;
