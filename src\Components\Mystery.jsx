import ScrollStack, { ScrollStackItem } from "../Constants/ScrollStack";

const mysteryBoxes = [
  {
    id: 1,
    title: "LUNAR MYSTERY BOX",
    price: "$100",
    image: "https://overlaysnow.com/cdn/shop/files/Silver_large.png?v=1745741431",
    description: "Discover cosmic treasures from the lunar surface",
    rarity: "Legendary",
    gradient: "from-slate-800 to-slate-900"
  },
  {
    id: 2,
    title: "STELLAR MYSTERY BOX",
    price: "$150",
    image: "https://overlaysnow.com/cdn/shop/files/Silver_large.png?v=1745741431",
    description: "Unlock secrets from distant galaxies",
    rarity: "Epic",
    gradient: "from-purple-800 to-purple-900"
  },
  {
    id: 3,
    title: "COSMIC MYSTERY BOX",
    price: "$200",
    image: "https://overlaysnow.com/cdn/shop/files/Silver_large.png?v=1745741431",
    description: "Experience the ultimate space adventure",
    rarity: "Mythic",
    gradient: "from-blue-800 to-blue-900"
  }
];

const Mystery = () => {
  return (
    <div className="w-full h-[75vh] mt-10 overflow-hidden">
      <div className="text-center mb-8 px-4">
        <h1 className="text-4xl sm:text-5xl lg:text-6xl font-semibold text-white mb-4 animate-pulse">
          Mystery Boxes
        </h1>
        <p className="text-lg sm:text-xl text-white/80 max-w-2xl mx-auto">
          Discover extraordinary treasures from across the cosmos
        </p>
      </div>
      <ScrollStack
        itemScale={0.04}
        itemStackDistance={40}
        baseScale={0.9}
        rotationAmount={2}
        blurAmount={1}
      >
        {mysteryBoxes.map((box, index) => (
          <ScrollStackItem key={box.id}>
            <div className={`w-full h-full bg-gradient-to-br ${box.gradient} p-4 sm:p-6 flex flex-col sm:flex-row justify-between items-center text-white rounded-2xl border border-white/10 backdrop-blur-sm relative overflow-hidden group`}>
              {/* Animated background effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

              {/* Content */}
              <div className="w-full sm:w-1/2 h-full flex flex-col justify-center items-center sm:items-start gap-4 relative z-10">
                <div className="text-center sm:text-left">
                  <div className="inline-block px-3 py-1 bg-white/20 rounded-full text-sm font-medium mb-3 backdrop-blur-sm">
                    {box.rarity}
                  </div>
                  <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-2 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                    {box.title}
                  </h2>
                  <p className="text-base sm:text-lg text-white/80 mb-4 max-w-md">
                    {box.description}
                  </p>
                  <div className="flex items-center gap-4">
                    <p className="text-2xl sm:text-3xl font-bold text-yellow-400">
                      {box.price}
                    </p>
                    <button className="px-6 py-2 bg-white/20 hover:bg-white/30 rounded-lg backdrop-blur-sm transition-all duration-300 hover:scale-105 border border-white/20">
                      Open Box
                    </button>
                  </div>
                </div>
              </div>

              {/* Image */}
              <div className="w-full sm:w-[40%] h-48 sm:h-full flex items-center justify-center relative">
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg" />
                <img
                  src={box.image}
                  alt={box.title}
                  className="w-full h-full object-cover object-center rounded-lg shadow-2xl transform group-hover:scale-105 transition-transform duration-500"
                />
                {/* Floating animation effect */}
                <div className="absolute inset-0 bg-gradient-to-t from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-pulse" />
              </div>
            </div>
          </ScrollStackItem>
        ))}
      </ScrollStack>
    </div>
  );
};

export default Mystery;
