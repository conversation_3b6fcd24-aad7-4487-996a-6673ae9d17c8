import ScrollStack, { ScrollStackItem } from "../Constants/ScrollStack";
const Mystery = () => {
  return (
    <div className="w-full h-[75vh] mt-10 overflow-hidden">
      <h1 className="text-6xl font-semibold text-white mb-5">Mystery...</h1>
      <ScrollStack>
        <ScrollStackItem>
          <div className="w-full h-full bg-gray-800 p-2 flex justify-between items-center text-white">
            <div className="w-1/2 h-full flex flex-col justify-center items-center gap-4">
              <h2 className="text-5xl">LUNAR MYSTERY BOX</h2>
              <p className="text-2xl">Price: $100</p>
            </div>
            <img src="https://overlaysnow.com/cdn/shop/files/Silver_large.png?v=1745741431" alt="" className="w-[40%] h-full object-cover object-center"/>
          </div>
        </ScrollStackItem>
        <ScrollStackItem>
          <div className="w-full h-full bg-gray-800 p-2 flex justify-between items-center text-white">
            <div className="w-1/2 h-full flex flex-col justify-center items-center gap-4">
              <h2 className="text-5xl">LUNAR MYSTERY BOX</h2>
              <p className="text-2xl">Price: $100</p>
            </div>
            <img src="https://overlaysnow.com/cdn/shop/files/Silver_large.png?v=1745741431" alt="" className="w-[40%] h-full object-cover object-center"/>
          </div>
        </ScrollStackItem>
        <ScrollStackItem>
          <div className="w-full h-full bg-gray-800 p-2 flex justify-between items-center text-white">
            <div className="w-1/2 h-full flex flex-col justify-center items-center gap-4">
              <h2 className="text-5xl">LUNAR MYSTERY BOX</h2>
              <p className="text-2xl">Price: $100</p>
            </div>
            <img src="https://overlaysnow.com/cdn/shop/files/Silver_large.png?v=1745741431" alt="" className="w-[40%] h-full object-cover object-center"/>
          </div>
        </ScrollStackItem>
      </ScrollStack>
    </div>
  );
};

export default Mystery;
