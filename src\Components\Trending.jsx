import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/effect-creative';
import { EffectCreative, Autoplay} from 'swiper/modules';
import trend1 from "../assets/trend1.png";
import trend2 from "../assets/trend2.png";
import trend3 from "../assets/trend3.png";
import trend4 from "../assets/trend4.png";

const Trending = () => {
  return (
    <div className="w-full h-screen mt-10 overflow-hidden">
      <h1 className="text-6xl font-semibold text-white">Trending...</h1>
      <div className="swiper w-full h-[75vh] mt-10 overflow-hidden rounded-2xl">
        <Swiper  grabCursor={true}
        loop={true}
        autoplay={{delay: 2500}}
        effect={'creative'}
        creativeEffect={{
          prev: {
            shadow: true,
            translate: [0, 0, -400],
          },
          next: {
            translate: ['100%', 0, 0],
          },
        }}
        modules={[EffectCreative, Autoplay]}
        className="mySwiper w-full h-full">
          <SwiperSlide>
            <img src="https://overlaysnow.com/cdn/shop/files/Banner_1_6.Web.jpg?v=1742381544&width=2000" alt="" className="w-full h-full object-cover object-center"/>
          </SwiperSlide>
          <SwiperSlide>
            <img src="https://overlaysnow.com/cdn/shop/files/Banner_11.web1.jpg?v=1743761162&width=2000" alt="" className="w-full h-full object-cover object-center"/>
          </SwiperSlide>
          <SwiperSlide>
            <img src={trend1} alt="" className="w-full h-full object-cover object-center"/>
          </SwiperSlide>
          <SwiperSlide>
            <img src={trend3} alt="" className="w-full h-full object-cover object-center"/>
          </SwiperSlide>
        </Swiper>
      </div>
    </div>
  );
};

export default Trending;
